/* 基础样式 */
:root {
    --primary-color: #396cd8;
    --primary-dark: #2a5bb9;
    --secondary-color: #4CAF50;
    --text-color: #333;
    --text-light: #666;
    --background-color: #f6f6f6;
    --white: #ffffff;
    --shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', 'Avenir', 'Helvetica', 'Arial', sans-serif;
    font-size: 16px;
    line-height: 1.5;
    color: var(--text-color);
    background-color: var(--background-color);
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

section {
    padding: 80px 0;
}

.section-title {
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 50px;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background-color: var(--primary-color);
    border-radius: 2px;
}

/* 按钮样式 */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition);
    cursor: pointer;
    border: none;
    outline: none;
    box-shadow: var(--shadow);
}

.primary-btn {
    background-color: var(--primary-color);
    color: var(--white);
}

.primary-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
}

.secondary-btn {
    background-color: var(--white);
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.secondary-btn:hover {
    background-color: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
}

/* 导航栏 */
header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background-color: var(--white);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transition: var(--transition);
}

header.scrolled {
    padding: 10px 0;
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
}

.logo {
    display: flex;
    align-items: center;
}

.logo img {
    width: 40px;
    height: 40px;
    margin-right: 10px;
}

.logo span {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.nav-links {
    display: flex;
    gap: 30px;
}

.nav-links a {
    text-decoration: none;
    color: var(--text-color);
    font-weight: 500;
    transition: var(--transition);
    position: relative;
}

.nav-links a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: var(--transition);
}

.nav-links a:hover {
    color: var(--primary-color);
}

.nav-links a:hover::after {
    width: 100%;
}

.menu-toggle {
    display: none;
    font-size: 1.5rem;
    cursor: pointer;
}

/* 英雄区域 */
.hero {
    padding-top: 150px;
    padding-bottom: 100px;
    background: linear-gradient(135deg, #f6f6f6 0%, #e9f0ff 100%);
}

.hero .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.hero-content {
    flex: 1;
    max-width: 600px;
}

.hero-content h1 {
    font-size: 3rem;
    margin-bottom: 20px;
    color: var(--text-color);
}

.hero-content p {
    font-size: 1.2rem;
    color: var(--text-light);
    margin-bottom: 30px;
}

.hero-buttons {
    display: flex;
    gap: 15px;
}

.hero-image {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-image img {
    max-width: 100%;
    height: auto;
    border-radius: 10px;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.hero-image img:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

/* 功能特点 */
.features {
    background-color: var(--white);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.feature-card {
    background-color: var(--white);
    border-radius: 10px;
    padding: 30px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    text-align: center;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.feature-icon {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.feature-card h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
}

.feature-card p {
    color: var(--text-light);
}

/* 界面展示 */
.screenshots {
    background-color: #f0f4ff;
}

.screenshot-container {
    position: relative;
    max-width: 1000px;
    margin: 0 auto;
    margin-bottom: 40px !important; /* 为图片说明文字留出空间 */
    display: flex;
    align-items: center;
    justify-content: center;
}

.screenshot-slider {
    position: relative;
    overflow: hidden;
    flex: 1;
    width: 100%;
    /* 使用固定的宽高比来保持容器大小 */
    padding-bottom: 56.25%; /* 16:9 的宽高比 */
}

.screenshot {
    max-width: 100%;
    text-align: center;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transform: translateX(100%); /* 初始位置在右侧 */
    transition: transform 0.5s ease-in-out, opacity 0.5s ease-in-out;
    pointer-events: none; /* 防止非活动幻灯片接收点击事件 */
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.screenshot.active {
    opacity: 1;
    transform: translateX(0); /* 活动状态位于中央 */
    pointer-events: auto; /* 活动幻灯片可以接收点击事件 */
}

.screenshot.prev {
    transform: translateX(-100%); /* 上一张幻灯片位于左侧 */
    opacity: 0;
}

.screenshot.next {
    transform: translateX(100%); /* 下一张幻灯片位于右侧 */
    opacity: 0;
}

.screenshot img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 10px;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.screenshot img:hover {
    transform: scale(1.01);
    /*box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);*/
}

.screenshot p {
    margin-top: 15px;
    color: var(--text-light);
    position: absolute;
    bottom: -30px;
    left: 0;
    width: 100%;
    text-align: center;
}

.slider-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--white);
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: var(--transition);
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.slider-btn:hover {
    background-color: var(--primary-color);
    color: var(--white);
    /*transform: scale(1.01);*/
}

.prev-btn {
    left: -20px;
}

.next-btn {
    right: -20px;
}

/* 下载区域 */
.download {
    background: linear-gradient(135deg, #396cd8 0%, #2a5bb9 100%);
    color: var(--white);
}

.download .section-title {
    color: var(--white);
}

.download .section-title::after {
    background-color: var(--white);
}

.download-description {
    text-align: center;
    margin-bottom: 40px;
    font-size: 1.2rem;
}

.download-options {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
}

.download-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: var(--white);
    color: var(--primary-color);
    padding: 20px 40px;
    border-radius: 10px;
    text-decoration: none;
    transition: var(--transition);
    box-shadow: var(--shadow);
}

.download-btn i {
    font-size: 2.5rem;
    margin-bottom: 10px;
}

.download-btn span {
    font-weight: 600;
}

.download-btn:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

/* 联系我们 */
.contact {
    background-color: var(--white);
}

.contact-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 50px;
}

.contact-info {
    flex: 1;
}

.contact-item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.contact-item i {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-right: 15px;
}

.contact-description {
    color: var(--text-light);
    line-height: 1.6;
}

.qr-code {
    flex: 1;
    display: flex;
    justify-content: center;
}

.qr-placeholder {
    width: 200px;
    height: 200px;
    background-color: #f0f4ff;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    box-shadow: var(--shadow);
}

.qr-placeholder i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 15px;
}

.qr-placeholder img {
    width: 100%;
    height: auto;
    border-radius: 10px;
}



/* 页脚 */
footer {
    background-color: #2a2a2a;
    color: var(--white);
    padding: 40px 0;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-logo {
    display: flex;
    align-items: center;
}

.footer-logo img {
    width: 30px;
    height: 30px;
    margin-right: 10px;
}

.footer-logo span {
    font-size: 1.2rem;
    font-weight: 700;
}

.copyright {
    color: #aaa;
    /*不要下划线*/
    text-decoration: none;
}

/* 返回顶部按钮 */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: var(--white);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    box-shadow: var(--shadow);
    border: none;
    outline: none;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background-color: var(--primary-dark);
    transform: translateY(-5px);
}

/* 响应式设计 */
@media (max-width: 992px) {
    .hero .container {
        flex-direction: column;
        text-align: center;
    }

    .hero-content {
        margin-bottom: 50px;
    }

    .hero-buttons {
        justify-content: center;
    }

    .contact-content {
        flex-direction: column;
    }

    .contact-info, .qr-code {
        width: 100%;
    }
}

@media (max-width: 768px) {
    .nav-links {
        display: none;
    }

    .menu-toggle {
        display: block;
    }

    .nav-links.active {
        display: flex;
        flex-direction: column;
        position: absolute;
        top: 80px;
        left: 0;
        width: 100%;
        background-color: var(--white);
        padding: 20px;
        box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .footer-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .screenshot-container {
        max-width: 90%;
    }

    .slider-btn {
        width: 35px;
        height: 35px;
    }

    .prev-btn {
        left: -10px;
    }

    .next-btn {
        right: -10px;
    }
}

/* 暗色模式 */
/*@media (prefers-color-scheme: dark) {*/
/*    :root {*/
/*        --text-color: #f6f6f6;*/
/*        --text-light: #ccc;*/
/*        --background-color: #2f2f2f;*/
/*        --white: #1a1a1a;*/
/*    }*/

/*    header {*/
/*        background-color: #1a1a1a;*/
/*    }*/

/*    .hero {*/
/*        background: linear-gradient(135deg, #2f2f2f 0%, #1a1a1a 100%);*/
/*    }*/

/*    .feature-card {*/
/*        background-color: #2a2a2a;*/
/*    }*/

/*    .screenshots {*/
/*        background-color: #222;*/
/*    }*/

/*    .download {*/
/*        background: linear-gradient(135deg, #2a5bb9 0%, #1a4aa8 100%);*/
/*    }*/

/*    .download-btn {*/
/*        background-color: #2a2a2a;*/
/*        color: var(--white);*/
/*    }*/

/*    .contact {*/
/*        background-color: #1a1a1a;*/
/*    }*/

/*    .qr-placeholder {*/
/*        background-color: #2a2a2a;*/
/*    }*/

/*    footer {*/
/*        background-color: #111;*/
/*    }*/
/*}*/

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.animate-pulse {
    animation: pulse 2s infinite;
}
