// 初始化AOS动画库
AOS.init({
    duration: 800,
    easing: 'ease',
    once: true,
    offset: 100
});

// DOM元素加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 导航栏滚动效果
    const header = document.querySelector('header');
    const backToTopBtn = document.getElementById('back-to-top');

    window.addEventListener('scroll', function() {
        if (window.scrollY > 50) {
            header.classList.add('scrolled');
            backToTopBtn.classList.add('visible');
        } else {
            header.classList.remove('scrolled');
            backToTopBtn.classList.remove('visible');
        }
    });

    // 返回顶部按钮
    backToTopBtn.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });

    // 移动端菜单切换
    const menuToggle = document.querySelector('.menu-toggle');
    const navLinks = document.querySelector('.nav-links');

    menuToggle.addEventListener('click', function() {
        navLinks.classList.toggle('active');
    });

    // 点击导航链接后关闭移动端菜单
    const navItems = document.querySelectorAll('.nav-links a');
    navItems.forEach(item => {
        item.addEventListener('click', function() {
            if (navLinks.classList.contains('active')) {
                navLinks.classList.remove('active');
            }
        });
    });

    // 下载按钮点击事件
    // const downloadBtns = document.querySelectorAll('.download-btn');
    // downloadBtns.forEach(btn => {
    //     btn.addEventListener('click', function(e) {
    //         e.preventDefault();
    //         alert('下载功能即将上线，敬请期待！');
    //     });
    // });

    // 添加截图轮播功能
    const screenshots = document.querySelectorAll('.screenshot');
    const prevBtn = document.querySelector('.prev-btn');
    const nextBtn = document.querySelector('.next-btn');

    if (screenshots.length > 1) {
        let currentIndex = 0;
        let isTransitioning = false; // 添加过渡状态标志

        // 初始化时只显示第一张图片
        screenshots.forEach((screenshot, index) => {
            if (index === 0) {
                screenshot.classList.add('active');
            } else if (index === 1) {
                screenshot.classList.add('next'); // 第二张图片初始化为下一张
            } else if (index === screenshots.length - 1) {
                screenshot.classList.add('prev'); // 最后一张图片初始化为上一张
            }
        });

        // 更新幻灯片状态的函数
        const updateSlideClasses = () => {
            const prevIndex = (currentIndex - 1 + screenshots.length) % screenshots.length;
            const nextIndex = (currentIndex + 1) % screenshots.length;

            // 清除所有幻灯片的状态类
            screenshots.forEach(slide => {
                slide.classList.remove('active', 'prev', 'next');
            });

            // 设置当前、上一张和下一张幻灯片的状态
            screenshots[currentIndex].classList.add('active');
            screenshots[prevIndex].classList.add('prev');
            screenshots[nextIndex].classList.add('next');
        };

        // 切换到下一张图片的函数
        const showNextSlide = () => {
            // 防止快速点击导致的闪烁
            if (isTransitioning) return;
            isTransitioning = true;

            currentIndex = (currentIndex + 1) % screenshots.length;
            updateSlideClasses();

            // 过渡结束后重置标志
            setTimeout(() => {
                isTransitioning = false;
            }, 500);
        };

        // 切换到上一张图片的函数
        const showPrevSlide = () => {
            // 防止快速点击导致的闪烁
            if (isTransitioning) return;
            isTransitioning = true;

            currentIndex = (currentIndex - 1 + screenshots.length) % screenshots.length;
            updateSlideClasses();

            // 过渡结束后重置标志
            setTimeout(() => {
                isTransitioning = false;
            }, 500);
        };

        // 添加点击事件
        nextBtn.addEventListener('click', showNextSlide);
        prevBtn.addEventListener('click', showPrevSlide);

        // 自动轮播
        let slideInterval = setInterval(() => {
            // 只有在没有过渡进行时才执行自动轮播
            if (!isTransitioning) {
                showNextSlide();
            }
        }, 5000); // 增加轮播间隔时间，让用户有更多时间查看图片

        // 鼠标悬停在轮播区域时暂停自动轮播
        const sliderContainer = document.querySelector('.screenshot-container');
        sliderContainer.addEventListener('mouseenter', () => {
            clearInterval(slideInterval);
        });

        // 鼠标移出轮播区域时恢复自动轮播
        sliderContainer.addEventListener('mouseleave', () => {
            slideInterval = setInterval(() => {
                // 只有在没有过渡进行时才执行自动轮播
                if (!isTransitioning) {
                    showNextSlide();
                }
            }, 5000);
        });

        // 使按钮在移动设备上更加明显
        const updateButtonsVisibility = () => {
            if (window.innerWidth < 768) {
                prevBtn.style.opacity = '1';
                nextBtn.style.opacity = '1';
            }
        };

        // 初始化时调用
        updateButtonsVisibility();

        // 窗口大小变化时调用
        window.addEventListener('resize', updateButtonsVisibility);
    }

    // 为功能卡片添加鼠标悬停动画
    const featureCards = document.querySelectorAll('.feature-card');
    featureCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.querySelector('.feature-icon').classList.add('animate-pulse');
        });

        card.addEventListener('mouseleave', function() {
            this.querySelector('.feature-icon').classList.remove('animate-pulse');
        });
    });

    // 平滑滚动到锚点
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();

            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);

            if (targetElement) {
                const headerHeight = header.offsetHeight;
                const targetPosition = targetElement.getBoundingClientRect().top + window.pageYOffset - headerHeight;

                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });
});
