import React from 'react';
import { Link } from 'react-router-dom';
import { Trophy, LogIn, UserPlus } from 'lucide-react';

interface PublicLayoutProps {
  children: React.ReactNode;
}

const PublicLayout: React.FC<PublicLayoutProps> = ({ children }) => {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航栏 */}
      <nav className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            {/* 左侧 Logo 和导航 */}
            <div className="flex items-center">
              <Link to="/plaza" className="flex items-center space-x-2">
                <Trophy className="h-8 w-8 text-blue-600" />
                <span className="text-xl font-bold text-gray-900">P-Box</span>
              </Link>
              <div className="ml-8">
                <Link
                  to="/plaza"
                  className="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium"
                >
                  项目广场
                </Link>
              </div>
            </div>

            {/* 右侧登录注册按钮 */}
            <div className="flex items-center space-x-4">
              <Link
                to="/login"
                className="flex items-center space-x-1 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium"
              >
                <LogIn className="h-4 w-4" />
                <span>登录</span>
              </Link>
              <Link
                to="/register"
                className="flex items-center space-x-1 bg-blue-600 text-white hover:bg-blue-700 px-4 py-2 rounded-md text-sm font-medium"
              >
                <UserPlus className="h-4 w-4" />
                <span>注册</span>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* 主要内容区域 */}
      <main className="max-w-7xl mx-auto">
        {children}
      </main>

      {/* 页脚 */}
      <footer className="bg-white border-t border-gray-200 mt-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center text-gray-500 text-sm">
            <p>&copy; 2024 P-Box Update Server. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default PublicLayout;
