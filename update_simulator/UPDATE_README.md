# Tauri 应用更新模拟工具

这个仓库包含了用于模拟 Tauri 应用程序更新流程的 Python 脚本。这些脚本可以帮助您测试更新服务器的功能，并模拟 Tauri 应用程序检查和安装更新的过程。

## 脚本说明

### 1. 完整的更新模拟器 (`tauri_update_simulator.py`)

这个脚本提供了完整的 Tauri 应用程序更新流程模拟，包括：

- 检查更新
- 显示更新通知
- 下载更新文件
- 显示下载进度
- 安装更新
- 显示安装进度

**使用方法:**

```bash
# 基本用法
python tauri_update_simulator.py

# 指定应用信息和服务器
python tauri_update_simulator.py --app-name "MyApp" --version "1.0.0" --server "http://localhost:8000"

# 指定平台和架构
python tauri_update_simulator.py --platform windows --arch x64

# 仅检查更新
python tauri_update_simulator.py --check-only

# 仅下载更新，不安装
python tauri_update_simulator.py --download-only

# 指定下载目录
python tauri_update_simulator.py --output-dir ./downloads
```

### 2. 简单的更新客户端 (`simple_update_client.py`)

这个脚本提供了更简单的更新流程模拟，专注于与更新服务器的交互：

- 检查更新
- 下载更新文件

**使用方法:**

```bash
# 基本用法
python simple_update_client.py

# 指定版本和服务器
python simple_update_client.py --version "1.0.0" --server "http://localhost:8000"

# 指定平台和架构
python simple_update_client.py --platform windows --arch x64

# 仅检查更新
python simple_update_client.py --check-only

# 指定下载目录
python simple_update_client.py --output-dir ./downloads
```

## 功能特点

### 自动检测平台和架构

如果未指定平台和架构，脚本会自动检测当前系统的平台和架构：

- **平台**: windows, macos, linux
- **架构**: x64, x86, arm64, arm

### 模拟用户界面

完整的模拟器脚本提供了简单的命令行用户界面，模拟：

- 更新通知对话框
- 下载进度条
- 安装进度条
- 安装完成通知

### 与更新服务器交互

脚本使用标准的 HTTP 请求与更新服务器交互：

1. 发送检查更新请求，包含当前版本、平台和架构信息
2. 接收更新信息，包括新版本号、更新说明、下载地址和签名
3. 下载更新文件
4. 模拟验证签名和安装过程

## 与更新服务器集成

这些脚本设计用于与 Django 更新服务器一起使用，但也可以与任何符合以下 API 规范的更新服务器一起使用：

### 检查更新 API

- **端点**: `/api/check-update/`
- **方法**: POST
- **请求体**:
  ```json
  {
    "current_version": "1.0.0",
    "platform": "windows",
    "arch": "x64"
  }
  ```
- **成功响应** (有更新):
  ```json
  {
    "version": "1.1.0",
    "notes": "更新说明...",
    "pub_date": "2023-11-15T10:00:00Z",
    "platforms": ["windows", "macos"],
    "url": "http://server.com/api/download/1.1.0/windows/x64/",
    "signature": "签名字符串..."
  }
  ```
- **成功响应** (无更新): HTTP 204 No Content

### 下载更新 API

- **端点**: 由检查更新 API 返回的 URL
- **方法**: GET
- **响应**: 更新文件的二进制内容

## 注意事项

- 这些脚本仅用于测试和模拟，不应在生产环境中使用
- 实际的 Tauri 应用程序使用 Rust 实现的更新机制，可能与这些脚本的行为略有不同
- 在实际应用中，应该使用适当的加密库验证更新文件的签名
