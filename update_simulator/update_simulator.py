#!/usr/bin/env python
"""
Django更新服务器测试脚本
用于全面测试Tauri应用的Django更新服务器API
"""

import requests
import json
import argparse
import sys
import time
from datetime import datetime


def test_check_update(server_url, version, platform, arch, verbose=False):
    """测试检查更新API"""
    print(f"\n=== 测试检查更新API ===")

    # 构建请求数据
    data = {
        "current_version": version,
        "platform": platform,
        "arch": arch
    }

    headers = {
        "Content-Type": "application/json",
        "User-Agent": f"UpdateTest/{version} ({platform}; {arch})"
    }

    try:
        response = requests.post(
            f"{server_url}/api/check-update/",
            json=data,
            headers=headers
        )

        print(f"状态码: {response.status_code}")
        if verbose:
            print(f"请求头: {json.dumps(dict(response.request.headers), indent=2)}")
            print(f"请求体: {response.request.body}")

        if response.status_code == 200:
            update_info = response.json()
            print("更新可用:")
            print(json.dumps(update_info, indent=2, ensure_ascii=False))
            return update_info
        elif response.status_code == 204:
            print("已是最新版本")
            return None
        else:
            print(f"错误: {response.text}")
            return None
    except Exception as e:
        print(f"请求出错: {e}")
        return None


def test_download_update(server_url, version, platform, arch, verbose=False):
    """测试下载更新API"""
    print(f"\n=== 测试下载更新API ===")

    try:
        # 构建下载URL
        download_url = f"{server_url}/api/download/{version}/{platform}/{arch}/"

        headers = {
            "User-Agent": f"UpdateTest/{version} ({platform}; {arch})"
        }

        print(f"尝试下载: {download_url}")

        # 发送HEAD请求先检查文件是否存在
        head_response = requests.head(download_url, headers=headers)
        print(f"HEAD状态码: {head_response.status_code}")

        if head_response.status_code == 200:
            # 获取文件大小
            content_length = head_response.headers.get('Content-Length')
            if content_length:
                print(f"文件大小: {int(content_length) / 1024 / 1024:.2f} MB")

            # 如果需要实际下载文件，取消下面的注释
            if verbose:
                print("开始下载文件...")
                start_time = time.time()
                response = requests.get(download_url, headers=headers, stream=True)

                # 获取文件名
                content_disposition = response.headers.get('Content-Disposition', '')
                filename = None
                if 'filename=' in content_disposition:
                    filename = content_disposition.split('filename=')[1].strip('"\'')
                else:
                    filename = f"update-{version}-{platform}-{arch}.bin"

                # 保存文件
                with open(filename, 'wb') as f:
                    total_size = 0
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                            total_size += len(chunk)

                            # 打印进度
                            if content_length:
                                progress = total_size / int(content_length) * 100
                                sys.stdout.write(f"\r下载进度: {progress:.2f}%")
                                sys.stdout.flush()

                end_time = time.time()
                print(f"\n下载完成，耗时: {end_time - start_time:.2f} 秒")
                print(f"文件保存为: {filename}")
            else:
                print("文件存在，但未下载（使用 --verbose 参数下载）")

            return True
        else:
            print(f"文件不存在或无法访问: {head_response.text}")
            return False
    except Exception as e:
        print(f"下载出错: {e}")
        return False


def test_platforms_api(server_url):
    """测试平台列表API"""
    print(f"\n=== 测试平台列表API ===")

    try:
        response = requests.get(f"{server_url}/api/platforms/")

        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            platforms = response.json()
            print("支持的平台:")
            print(json.dumps(platforms, indent=2, ensure_ascii=False))
            return platforms
        else:
            print(f"错误: {response.text}")
            return None
    except Exception as e:
        print(f"请求出错: {e}")
        return None


def test_architectures_api(server_url):
    """测试架构列表API"""
    print(f"\n=== 测试架构列表API ===")

    try:
        response = requests.get(f"{server_url}/api/architectures/")

        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            architectures = response.json()
            print("支持的架构:")
            print(json.dumps(architectures, indent=2, ensure_ascii=False))
            return architectures
        else:
            print(f"错误: {response.text}")
            return None
    except Exception as e:
        print(f"请求出错: {e}")
        return None


def test_releases_api(server_url):
    """测试发布版本列表API"""
    print(f"\n=== 测试发布版本列表API ===")

    try:
        response = requests.get(f"{server_url}/api/releases/")

        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            releases = response.json()
            print("发布版本:")
            print(json.dumps(releases, indent=2, ensure_ascii=False))
            return releases
        else:
            print(f"错误: {response.text}")
            return None
    except Exception as e:
        print(f"请求出错: {e}")
        return None


def main(url="https://newonet.com/p-box"):
    parser = argparse.ArgumentParser(description="测试Tauri的Django更新服务器")
    parser.add_argument("--url", default=url, help="服务器URL")
    parser.add_argument("--version", default="0.0.1", help="当前版本号")
    parser.add_argument("--platform", default="macos", choices=["windows", "macos", "linux"], help="平台")
    parser.add_argument("--arch", default="aarch64", choices=["x64", "aarch64"], help="架构")
    parser.add_argument("--verbose", "-v", action="store_true", help="显示详细信息")
    parser.add_argument("--all", "-a", action="store_true", help="测试所有API")
    parser.add_argument("--check", "-c", action="store_true", help="测试检查更新API")
    parser.add_argument("--download", "-d", action="store_true", help="测试下载更新API")
    parser.add_argument("--platforms", "-p", action="store_true", help="测试平台列表API")
    parser.add_argument("--architectures", "-r", action="store_true", help="测试架构列表API")
    parser.add_argument("--releases", "-l", action="store_true", help="测试发布版本列表API")

    args = parser.parse_args()

    print(f"测试Django更新服务器: {args.url}")
    print(f"当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"当前版本: {args.version}")
    print(f"平台: {args.platform}")
    print(f"架构: {args.arch}")
    print("-" * 50)

    # 如果没有指定具体测试，则默认测试检查更新API
    if not (args.all or args.check or args.download or args.platforms or args.architectures or args.releases):
        args.check = True

    # 测试所有API
    if args.all:
        args.check = args.download = args.platforms = args.architectures = args.releases = True

    # 测试检查更新API
    if args.check:
        update_info = test_check_update(args.url, args.version, args.platform, args.arch, args.verbose)

    # 测试下载更新API
    if args.download:
        test_download_update(args.url, args.version, args.platform, args.arch, args.verbose)

    # 测试平台列表API
    if args.platforms:
        test_platforms_api(args.url)

    # 测试架构列表API
    if args.architectures:
        test_architectures_api(args.url)

    # 测试发布版本列表API
    if args.releases:
        test_releases_api(args.url)

    print("\n测试完成")
    return 0


if __name__ == "__main__":
    # https://newonet.com/p-box/api/check-update/
    # url = "https://newonet.com/p-box"
    url = "http://localhost:8000/p-box"
    sys.exit(main(url))
