<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>P-Box - 高效的项目任务管理工具</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="icon" href="images/logo.png" type="image/png">
    <!-- 引入Font Awesome图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- 引入AOS动画库 -->
    <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css"/>
</head>
<body>
<!-- 导航栏 -->
<header>
    <nav class="container">
        <div class="logo">
            <img src="images/logo.png" alt="P-Box Logo">
            <span>P-Box</span>
        </div>
        <div class="nav-links">
            <a href="#features">功能特点</a>
            <a href="#screenshots">界面展示</a>
            <a href="#download">立即下载</a>
            <a href="#contact">联系我们</a>
        </div>
        <div class="menu-toggle">
            <i class="fas fa-bars"></i>
        </div>
    </nav>
</header>

<!-- 英雄区域 -->
<section class="hero">
    <div class="container">
        <div class="hero-content" data-aos="fade-right">
            <h1>P-Box 项目任务管理器</h1>
            <p>高效管理您的项目任务，提升工作效率</p>
            <div class="hero-buttons">
                <a href="#download" class="btn primary-btn">立即下载</a>
                <a href="#features" class="btn secondary-btn">了解更多</a>
            </div>
        </div>
        <div class="hero-image" data-aos="fade-left">
            <img src="images/ui.png" alt="P-Box 应用截图">
        </div>
    </div>
</section>

<!-- 功能特点 -->
<section id="features" class="features">
    <div class="container">
        <h2 class="section-title" data-aos="fade-up">功能特点</h2>
        <div class="features-grid">
            <div class="feature-card" data-aos="fade-up" data-aos-delay="100">
                <div class="feature-icon">
                    <i class="fas fa-tasks"></i>
                </div>
                <h3>任务管理</h3>
                <p>轻松创建、编辑和管理项目任务，支持分类和搜索功能</p>
            </div>
            <div class="feature-card" data-aos="fade-up" data-aos-delay="200">
                <div class="feature-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <h3>定时执行</h3>
                <p>设置任务的执行周期，支持按天、小时、分钟定期执行</p>
            </div>
            <div class="feature-card" data-aos="fade-up" data-aos-delay="300">
                <div class="feature-icon">
                    <i class="fas fa-terminal"></i>
                </div>
                <h3>脚本执行</h3>
                <p>支持执行自定义脚本，可设置终止指令，灵活控制任务执行</p>
            </div>
            <div class="feature-card" data-aos="fade-up" data-aos-delay="400">
                <div class="feature-icon">
                    <i class="fas fa-file-export"></i>
                </div>
                <h3>导入导出</h3>
                <p>支持任务配置的导入导出，方便在不同设备间迁移</p>
            </div>
            <div class="feature-card" data-aos="fade-up" data-aos-delay="500">
                <div class="feature-icon">
                    <i class="fas fa-grip-horizontal"></i>
                </div>
                <h3>项目广场</h3>
                <p>将推出项目广场，分享好玩的项目</p>
            </div>
            <div class="feature-card" data-aos="fade-up" data-aos-delay="600">
                <div class="feature-icon">
                    <i class="fas fa-desktop"></i>
                </div>
                <h3>跨平台支持</h3>
                <p>支持Windows和macOS系统</p>
            </div>
        </div>
    </div>
</section>

<!-- 界面展示 -->
<section id="screenshots" class="screenshots">
    <div class="container">
        <h2 class="section-title" data-aos="fade-up">界面展示</h2>
        <div class="screenshot-container" data-aos="fade-up">
            <button class="slider-btn prev-btn"><i class="fas fa-chevron-left"></i></button>
            <div class="screenshot-slider">
                <div class="screenshot active">
                    <img src="images/ui.png" alt="任务列表界面">
                    <p>任务列表界面 - 直观管理所有任务</p>
                </div>
                <div class="screenshot">
                    <img src="images/ui-add.png" alt="任务添加界面">
                    <p>任务添加界面 - 脚本任务可定时执行</p>
                </div>
                <!-- 更多截图可以在这里添加 -->
            </div>
            <button class="slider-btn next-btn"><i class="fas fa-chevron-right"></i></button>
        </div>
    </div>
</section>

<!-- 下载区域 -->
<section id="download" class="download">
    <div class="container">
        <h2 class="section-title" data-aos="fade-up">立即下载</h2>
        <div class="download-options" data-aos="fade-up">
            <a href="https://newonet.com/media/updates/p-box 安装包-Windows版.zip" target="_blank" class="download-btn">
                <i class="fab fa-windows"></i>
                <span>Windows</span>
            </a>
            <a href="https://newonet.com/media/updates/p-box 安装包-Mac版.zip" target="_blank" class="download-btn">
                <i class="fab fa-apple"></i>
                <span>MacOS</span>
            </a>
        </div>
    </div>
</section>

<!-- 联系我们 -->
<section id="contact" class="contact">
    <div class="container">
        <h2 class="section-title" data-aos="fade-up">联系我们</h2>
        <div class="contact-content" data-aos="fade-up">
            <div class="contact-info">
                <div class="contact-item">
                    <i class="fab fa-weixin"></i>
                    <p>微信: mwddkf001</p>
                </div>
                <p class="contact-description">
                    如有任何问题或建议，欢迎通过微信联系我们。我们将尽快回复您的咨询。
                </p>
            </div>
            <div class="qr-code">
                <!-- 这里可以放置微信二维码图片 -->
                <div class="qr-placeholder">
                    <!--                        <i class="fab fa-weixin"></i>-->
                    <img src="images/wechat.png" alt="wechat">
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 页脚 -->
<footer>
    <div class="container">
        <div class="footer-content">
            <div class="footer-logo">
                <img src="images/logo.png" alt="P-Box Logo">
                <span>P-Box</span>
            </div>
            <a class="copyright" href="https://beian.miit.gov.cn/" target="_blank">皖ICP备18009967号-12</a>
            <p class="copyright">© 2025 newonet.com 保留所有权利。</p>
        </div>
    </div>
</footer>

<!-- 返回顶部按钮 -->
<button id="back-to-top" class="back-to-top">
    <i class="fas fa-arrow-up"></i>
</button>

<!-- JavaScript -->
<script src="https://unpkg.com/aos@next/dist/aos.js"></script>
<script src="script.js"></script>
</body>
</html>
