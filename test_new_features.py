#!/usr/bin/env python3
"""
测试新功能的脚本
"""

import requests
import json

BASE_URL = "http://localhost:8000/p-box/api"

def test_login():
    """测试登录获取token"""
    response = requests.post(f"{BASE_URL}/auth/login", json={
        "username": "spencer",
        "password": "Pp250509"
    })
    if response.status_code == 200:
        token = response.json()["access_token"]
        print("✅ 登录成功")
        return token
    else:
        print("❌ 登录失败")
        return None

def test_plaza_projects():
    """测试项目广场API"""
    response = requests.get(f"{BASE_URL}/plaza/")
    if response.status_code == 200:
        projects = response.json()
        print(f"✅ 项目广场API正常，返回 {len(projects)} 个项目")
        
        # 检查是否有官方精选项目
        featured_projects = [p for p in projects if p.get('is_featured')]
        high_score_projects = [p for p in projects if p.get('score', 0) > 20]
        
        print(f"   - 官方精选项目: {len(featured_projects)} 个")
        print(f"   - 高分项目(>20): {len(high_score_projects)} 个")
        
        return projects
    else:
        print("❌ 项目广场API失败")
        return []

def test_admin_projects(token):
    """测试管理员项目管理API"""
    headers = {"Authorization": f"Bearer {token}"}
    
    # 获取所有项目
    response = requests.get(f"{BASE_URL}/admin/projects/", headers=headers)
    if response.status_code == 200:
        projects = response.json()
        print(f"✅ 管理员项目列表API正常，返回 {len(projects)} 个项目")
        
        if projects:
            # 测试获取项目详情
            project_id = projects[0]["id"]
            detail_response = requests.get(f"{BASE_URL}/admin/projects/{project_id}", headers=headers)
            if detail_response.status_code == 200:
                detail = detail_response.json()
                print(f"✅ 项目详情API正常，项目: {detail['name']}")
                if detail.get('config_file_content'):
                    print("   - 包含配置文件内容")
                else:
                    print("   - 无配置文件内容")
            else:
                print("❌ 项目详情API失败")
            
            # 测试更新项目评分
            update_response = requests.put(
                f"{BASE_URL}/admin/projects/{project_id}",
                headers=headers,
                json={"score": 90}
            )
            if update_response.status_code == 200:
                print("✅ 项目评分更新API正常")
            else:
                print("❌ 项目评分更新API失败")
        
        return projects
    else:
        print("❌ 管理员项目列表API失败")
        return []

def test_user_projects():
    """测试用户高分项目API"""
    user_id = 3  # spencer3用户
    response = requests.get(f"{BASE_URL}/plaza/user/{user_id}")
    if response.status_code == 200:
        projects = response.json()
        print(f"✅ 用户高分项目API正常，用户{user_id}有 {len(projects)} 个高分项目")
        return projects
    else:
        print("❌ 用户高分项目API失败")
        return []

def main():
    print("🚀 开始测试新功能...")
    print("=" * 50)
    
    # 测试登录
    token = test_login()
    if not token:
        print("无法获取token，停止测试")
        return
    
    print()
    
    # 测试项目广场
    plaza_projects = test_plaza_projects()
    print()
    
    # 测试管理员功能
    admin_projects = test_admin_projects(token)
    print()
    
    # 测试用户项目
    user_projects = test_user_projects()
    print()
    
    print("=" * 50)
    print("🎉 测试完成！")
    
    # 总结
    print("\n📊 功能总结:")
    print(f"- 项目广场: {len(plaza_projects)} 个高分项目")
    print(f"- 管理员管理: {len(admin_projects)} 个项目可管理")
    print(f"- 用户项目展示: 正常")

if __name__ == "__main__":
    main()
